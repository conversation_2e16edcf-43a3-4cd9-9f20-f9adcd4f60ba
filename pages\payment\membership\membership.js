const app = getApp();
const paymentApi = require('../../../utils/api/paymentApi.js');

Page({
  data: {
    membershipPlans: [],
    selectedPlan: null,
    selectedPlanInfo: {},
    showPaymentModal: false,
    paymentLoading: false,
    benefits: [
      {
        icon: '/pages/payment/membership/images/unlimited.png',
        text: '无限制生成PDF'
      },
      {
        icon: '/pages/payment/membership/images/hd.png',
        text: '高清简历导出'
      },
      {
        icon: '/pages/payment/membership/images/template.png',
        text: '专属简历模板'
      },
      {
        icon: '/pages/payment/membership/images/support.png',
        text: '优先客服支持'
      }
    ]
  },

  onLoad() {
    this.loadMembershipPlans();
  },

  // 加载会员套餐
  async loadMembershipPlans() {
    wx.showLoading({ title: '加载中...' });

    try {
      const result = await paymentApi.getMembershipPlans();
      
      if (result.success && result.data) {
        this.setData({
          membershipPlans: result.data
        });
      } else {
        // 使用默认套餐数据
        this.setData({
          membershipPlans: this.getDefaultPlans()
        });
      }
    } catch (error) {
      console.error('加载套餐失败:', error);
      this.setData({
        membershipPlans: this.getDefaultPlans()
      });
    }

    wx.hideLoading();
  },

  // 获取默认套餐数据
  getDefaultPlans() {
    return [
      {
        id: 'monthly',
        name: '月度会员',
        duration: '1个月',
        price: 19.9,
        original_price: null,
        description: '适合短期使用',
        discount: null
      },
      {
        id: 'quarterly',
        name: '季度会员',
        duration: '3个月',
        price: 49.9,
        original_price: 59.7,
        description: '性价比之选',
        discount: '8.4折'
      },
      {
        id: 'yearly',
        name: '年度会员',
        duration: '12个月',
        price: 168.0,
        original_price: 238.8,
        description: '最超值选择',
        discount: '7折'
      },
      {
        id: 'permanent',
        name: '永久会员',
        duration: '永久有效',
        price: 298.0,
        original_price: null,
        description: '一次购买，终身享受',
        discount: '限时'
      }
    ];
  },

  // 选择套餐
  selectPlan(e) {
    const plan = e.currentTarget.dataset.plan;
    this.setData({
      selectedPlan: plan.id,
      selectedPlanInfo: plan
    });
  },

  // 创建订单
  createOrder() {
    if (!this.data.selectedPlan) {
      wx.showToast({
        title: '请选择套餐',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showPaymentModal: true
    });
  },

  // 隐藏支付弹窗
  hidePaymentModal() {
    this.setData({
      showPaymentModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 确认支付
  async confirmPayment() {
    const { selectedPlanInfo } = this.data;
    
    this.setData({ paymentLoading: true });

    try {
      // 创建订单
      const orderResult = await paymentApi.createOrder({
        plan_id: selectedPlanInfo.id,
        plan_name: selectedPlanInfo.name,
        amount: selectedPlanInfo.price
      });

      if (!orderResult.success) {
        throw new Error(orderResult.message || '创建订单失败');
      }

      // 发起微信支付
      const paymentResult = await paymentApi.requestWxPayment(orderResult.data.payment_data);

      if (paymentResult.success) {
        // 支付成功
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 更新会员状态
        app.globalData.isMember = true;
        if (selectedPlanInfo.id === 'permanent') {
          app.globalData.membershipExpiry = 'permanent';
        } else {
          // 计算到期时间
          const now = new Date();
          const months = this.getPlanMonths(selectedPlanInfo.id);
          now.setMonth(now.getMonth() + months);
          app.globalData.membershipExpiry = now.toISOString();
        }

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else if (!paymentResult.cancelled) {
        throw new Error(paymentResult.message || '支付失败');
      }

    } catch (error) {
      console.error('支付流程失败:', error);
      wx.showToast({
        title: error.message || '支付失败',
        icon: 'none'
      });
    }

    this.setData({ 
      paymentLoading: false,
      showPaymentModal: false
    });
  },

  // 获取套餐月数
  getPlanMonths(planId) {
    const monthsMap = {
      'monthly': 1,
      'quarterly': 3,
      'yearly': 12,
      'permanent': 999
    };
    return monthsMap[planId] || 1;
  }
});