/**
 * 简历相关API接口
 */
const request = require('./request');
const apiConfig = require('../../config/apiConfig');

/**
 * 生成简历预览图片（新版本 - 返回图片URL）
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @param {Object} options 可选参数
 * @returns {Promise<Object>} 包含图片URL的响应对象
 */
function generatePreviewImage(resumeData, themeConfig, templateId, options = {}) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId,
    // 添加图片优化参数
    quality: options.quality || 75,
    max_width: options.maxWidth || 1200,
    max_height: options.maxHeight || 3600
  };

  // 添加调试日志
  console.log('=== 预览图片API请求（新版本）===');
  console.log('模板ID:', templateId);
  console.log('主题配置:', themeConfig);
  console.log('图片参数:', { quality: requestData.quality, max_width: requestData.max_width, max_height: requestData.max_height });
  console.log('请求数据:', requestData);



  return request.request({
    url: apiConfig.exportJpegUrl,
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'text', // 改为text，因为现在返回JSON而不是二进制数据
    showLoading: false,
    showError: false,
    needAuth: true, // 根据文档，认证是可选的
    timeout: apiConfig.timeout?.previewImage || 10000 // 10秒超时
  });
}

/**
 * 生成PDF文件（新版本 - 返回PDF URL）
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @returns {Promise<Object>} 包含PDF URL的响应对象
 */
function generatePDF(resumeData, themeConfig, templateId) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId
  };

  // 添加调试日志
  console.log('=== PDF生成API请求（新版本）===');
  console.log('模板ID:', templateId);
  console.log('主题配置:', themeConfig);
  console.log('请求数据:', requestData);


  return request.request({
    url: apiConfig.generatePDFUrl,
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'text', // 改为text，因为现在返回JSON而不是二进制数据
    showLoading: false, // 关闭自动loading，由调用方控制
    showError: false, // 关闭自动错误提示，由调用方处理
    needAuth: true, // 根据文档，认证是可选的
    timeout: apiConfig.timeout?.generatePDF || 10000 // 10秒超时
  });
}

module.exports = {
  generatePreviewImage,
  generatePDF
};
