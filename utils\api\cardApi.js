/**
 * 卡密系统相关API接口
 * 对应服务端API文档中的卡密系统模块 (/cards) 和增强权限检查模块 (/permissions)
 */
const request = require('../request.js');

/**
 * 卡密系统API类
 */
class CardApi {

  /**
   * 用户兑换卡密获取权益
   * 对应接口: POST /cards/redeem
   * @param {string} cardCode 卡密代码
   * @returns {Promise<Object>} 兑换结果响应
   */
  async redeemCard(cardCode) {
    try {
      console.log('=== 兑换卡密API请求 ===');
      console.log('卡密代码:', cardCode);

      const response = await request.post('/cards/redeem', {
        card_code: cardCode
      }, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('兑换卡密失败:', error);
      return { success: false, message: '兑换失败，请检查卡密是否正确' };
    }
  }

  /**
   * 获取用户当前的权益汇总信息
   * 对应接口: GET /cards/benefits
   * @returns {Promise<Object>} 权益汇总响应
   */
  async getCardBenefits() {
    try {
      console.log('=== 获取卡密权益API请求 ===');

      const response = await request.get('/cards/benefits', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取卡密权益失败:', error);
      return { success: false, message: '获取卡密权益失败' };
    }
  }

  /**
   * 验证卡密有效性（不实际兑换）
   * 对应接口: GET /cards/validate/{card_code}
   * @param {string} cardCode 卡密代码
   * @returns {Promise<Object>} 验证结果响应
   */
  async validateCard(cardCode) {
    try {
      console.log('=== 验证卡密API请求 ===');
      console.log('卡密代码:', cardCode);

      const response = await request.get(`/cards/validate/${cardCode}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('验证卡密失败:', error);
      return { success: false, message: '验证卡密失败' };
    }
  }

  /**
   * 检查用户是否有足够的次数权益
   * 对应接口: GET /cards/quota/check
   * @param {string} featureName 功能名称
   * @param {number} quotaNeeded 需要的次数，默认为1
   * @returns {Promise<Object>} 次数权益检查响应
   */
  async checkQuotaAvailability(featureName, quotaNeeded = 1) {
    try {
      console.log('=== 检查次数权益API请求 ===');
      console.log('功能名称:', featureName);
      console.log('需要次数:', quotaNeeded);

      const queryParams = new URLSearchParams();
      queryParams.append('feature_name', featureName);
      queryParams.append('quota_needed', quotaNeeded.toString());

      const response = await request.get(`/cards/quota/check?${queryParams.toString()}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('检查次数权益失败:', error);
      return { success: false, message: '检查次数权益失败' };
    }
  }

  /**
   * 消费用户的次数权益
   * 对应接口: POST /cards/quota/consume
   * @param {string} featureName 功能名称
   * @param {number} quotaNeeded 需要消费的次数，默认为1
   * @returns {Promise<Object>} 消费结果响应
   */
  async consumeQuota(featureName, quotaNeeded = 1) {
    try {
      console.log('=== 消费次数权益API请求 ===');
      console.log('功能名称:', featureName);
      console.log('消费次数:', quotaNeeded);

      const response = await request.post('/cards/quota/consume', {
        feature_name: featureName,
        quota_needed: quotaNeeded
      }, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('消费次数权益失败:', error);
      return { success: false, message: '消费次数权益失败' };
    }
  }

  /**
   * 获取用户的卡密兑换历史
   * 对应接口: GET /cards/history
   * @param {Object} params 查询参数
   * @param {number} params.page 页码，默认为1
   * @param {number} params.page_size 每页数量，默认为20
   * @returns {Promise<Object>} 兑换历史响应
   */
  async getCardHistory(params = {}) {
    try {
      console.log('=== 获取卡密兑换历史API请求 ===');
      console.log('查询参数:', params);

      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.page_size) queryParams.append('page_size', params.page_size.toString());

      const queryString = queryParams.toString();
      const url = `/cards/history${queryString ? '?' + queryString : ''}`;

      const response = await request.get(url, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取卡密兑换历史失败:', error);
      return { success: false, message: '获取卡密兑换历史失败' };
    }
  }

  // === 增强权限检查模块方法 ===

  /**
   * 检查功能权限，集成卡密权益和会员权益
   * 对应接口: GET /permissions/check/{feature}
   * @param {string} feature 功能名称
   * @param {number} quotaNeeded 需要的次数，默认为1
   * @returns {Promise<Object>} 权限检查响应
   */
  async checkPermission(feature, quotaNeeded = 1) {
    try {
      console.log('=== 检查功能权限API请求 ===');
      console.log('功能名称:', feature);
      console.log('需要次数:', quotaNeeded);

      const queryParams = new URLSearchParams();
      queryParams.append('quota_needed', quotaNeeded.toString());

      const response = await request.get(`/permissions/check/${feature}?${queryParams.toString()}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('检查功能权限失败:', error);
      return { success: false, message: '检查功能权限失败' };
    }
  }

  /**
   * 消费功能权限，根据权益来源自动选择消费方式
   * 对应接口: POST /permissions/consume/{feature}
   * @param {string} feature 功能名称
   * @param {number} quotaNeeded 需要的次数，默认为1
   * @param {string} templateId 模板ID（可选）
   * @returns {Promise<Object>} 消费结果响应
   */
  async consumePermission(feature, quotaNeeded = 1, templateId = '') {
    try {
      console.log('=== 消费功能权限API请求 ===');
      console.log('功能名称:', feature);
      console.log('需要次数:', quotaNeeded);
      console.log('模板ID:', templateId);

      const requestData = {
        quota_needed: quotaNeeded
      };
      if (templateId) {
        requestData.template_id = templateId;
      }

      const response = await request.post(`/permissions/consume/${feature}`, requestData, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('消费功能权限失败:', error);
      return { success: false, message: '消费功能权限失败' };
    }
  }

  /**
   * 获取用户完整的权限汇总信息
   * 对应接口: GET /permissions/summary
   * @returns {Promise<Object>} 权限汇总响应
   */
  async getPermissionSummary() {
    try {
      console.log('=== 获取权限汇总API请求 ===');

      const response = await request.get('/permissions/summary', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取权限汇总失败:', error);
      return { success: false, message: '获取权限汇总失败' };
    }
  }

  /**
   * 获取详细的权益信息，包括使用统计
   * 对应接口: GET /permissions/benefits/detailed
   * @returns {Promise<Object>} 详细权益信息响应
   */
  async getDetailedBenefits() {
    try {
      console.log('=== 获取详细权益信息API请求 ===');

      const response = await request.get('/permissions/benefits/detailed', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取详细权益信息失败:', error);
      return { success: false, message: '获取详细权益信息失败' };
    }
  }

  /**
   * 获取用户当前可用的功能列表
   * 对应接口: GET /permissions/features/available
   * @returns {Promise<Object>} 可用功能列表响应
   */
  async getAvailableFeatures() {
    try {
      console.log('=== 获取可用功能列表API请求 ===');

      const response = await request.get('/permissions/features/available', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取可用功能列表失败:', error);
      return { success: false, message: '获取可用功能列表失败' };
    }
  }
}

module.exports = new CardApi();