const apiConfig = require('../../config/apiConfig.js');
const request = require('../request.js');

/**
 * 卡密系统API
 */
class CardApi {
  
  /**
   * 兑换卡密
   * @param {string} cardCode 卡密代码
   */
  async redeemCard(cardCode) {
    try {
      const response = await request.post(apiConfig.redeemCardUrl, {
        card_code: cardCode
      });
      return response;
    } catch (error) {
      console.error('兑换卡密失败:', error);
      return { success: false, message: '兑换失败，请检查卡密是否正确' };
    }
  }

  /**
   * 查询卡密信息
   * @param {string} cardCode 卡密代码
   */
  async getCardInfo(cardCode) {
    try {
      const response = await request.get(`${apiConfig.cardInfoUrl}/${cardCode}`);
      return response;
    } catch (error) {
      console.error('查询卡密信息失败:', error);
      return { success: false, message: '查询卡密信息失败' };
    }
  }
}

module.exports = new CardApi();