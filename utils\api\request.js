/**
 * 统一的API请求工具
 * 自动处理token、userId、loading、错误等
 * 支持自动token刷新和无感重新授权
 * 支持多商户source参数自动添加
 */
const apiConfig = require('../../config/apiConfig');
const tokenManager = require('../auth/tokenManager');
const networkChecker = require('../network/networkChecker');

/**
 * 发起API请求（支持401错误自动重试）
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址（相对路径或完整URL）
 * @param {string} options.method 请求方法，默认GET
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 额外的请求头
 * @param {boolean} options.showLoading 是否显示loading，默认true
 * @param {string} options.loadingText loading文字，默认"加载中..."
 * @param {boolean} options.showError 是否显示错误提示，默认true
 * @param {boolean} options.needAuth 是否需要认证，默认true
 * @param {string} options.responseType 响应类型，默认为空
 * @param {number} options.timeout 请求超时时间（毫秒），默认使用配置中的值
 * @param {boolean} options._isRetry 是否为重试请求（内部使用）
 * @param {string} options.merchantCode 商户代码（可选，默认使用配置中的商户代码）
 * @param {boolean} options.autoAddSource 是否自动添加source参数，默认true
 * @returns {Promise} 请求Promise
 */
async function request(options = {}) {
  const maxRetries = options.maxRetries || 2;
  let retryCount = 0;

  while (retryCount <= maxRetries) {
    try {
      // 执行请求
      return await executeRequest(options);
    } catch (error) {
      // 如果是401错误且不是重试请求，尝试重新登录后重试
      if (options.needAuth !== false && !options._isRetry && isAuthError(error)) {
        console.log('检测到401错误，尝试重新登录后重试...');

        try {
          // 自动重新登录
          await tokenManager.autoReLogin();
          console.log('重新登录成功，重试请求...');

          // 标记为重试请求，避免无限递归
          const retryOptions = { ...options, _isRetry: true };
          return await executeRequest(retryOptions);

        } catch (loginError) {
          console.error('重新登录失败:', loginError);

          // 登录失败，跳转到首页
          if (options.showError !== false) {
            wx.showToast({
              title: '登录失效，返回首页',
              icon: 'none',
              duration: 2000
            });
          }

          // 延迟跳转到首页
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/index/index'
            });
          }, 2000);

          throw loginError;
        }
      }

      // 如果是网络错误且还有重试次数，进行重试
      if (error.isNetworkError && retryCount < maxRetries) {
        retryCount++;
        console.log(`网络请求失败，进行第${retryCount}次重试...`);

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 500 * retryCount));
        continue;
      }

      throw error;
    }
  }
}

/**
 * 执行实际的网络请求
 * @param {Object} options 请求配置
 * @returns {Promise} 请求Promise
 */
function executeRequest(options) {
  return new Promise((resolve, reject) => {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      showLoading = false,
      loadingText = '加载中...',
      showError = false,
      needAuth = true,
      responseType,
      timeout
    } = options;

    // 获取超时时间
    const timeoutMs = timeout || apiConfig.timeout?.default || 5000;

    // 构建完整URL（包含source参数）
    const fullUrl = buildUrlWithSource(url, options);

    // 构建请求头
    let requestHeader = {
      'Content-Type': 'application/json',
      ...header
    };

    // 添加认证信息
    if (needAuth) {
      const authHeader = getAuthHeader();
      requestHeader = { ...requestHeader, ...authHeader };
    }

    // 显示loading
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    // 设置超时定时器
    let timeoutTimer = null;
    let isCompleted = false;

    if (timeoutMs > 0) {
      timeoutTimer = setTimeout(() => {
        if (!isCompleted) {
          isCompleted = true;

          if (showLoading) {
            wx.hideLoading();
          }

          const timeoutError = new Error('请求超时，请检查网络连接');
          timeoutError.isTimeout = true;
          timeoutError.isNetworkError = true;

          console.error(`API请求超时 [${method.toUpperCase()}] ${fullUrl}: ${timeoutMs}ms`);

          if (showError) {
            wx.showToast({
              title: '请求超时，请重试',
              icon: 'none',
              duration: 3000
            });
          }

          reject(timeoutError);
        }
      }, timeoutMs);
    }

    // 构建请求配置
    const requestConfig = {
      url: fullUrl,
      method: method.toUpperCase(),
      data: data,
      header: requestHeader,
      success: (res) => {
        // 检查是否已经超时
        if (isCompleted) {
          return;
        }
        isCompleted = true;

        // 清除超时定时器
        if (timeoutTimer) {
          clearTimeout(timeoutTimer);
        }

        console.log(`API请求成功 [${method.toUpperCase()}] ${fullUrl}:`, res.statusCode);

        // 如果是arraybuffer类型，直接返回数据
        if (responseType === 'arraybuffer') {
          resolve(res.data);
          return;
        }

        // 处理业务逻辑
        if (res.statusCode === 200) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 401认证错误
          const error = new Error('认证失败');
          error.statusCode = 401;
          error.isAuthError = true;

          // 如果是401错误且不是重试请求，记录错误并准备跳转
          if (!options._isRetry) {
            console.log('检测到401认证错误，准备跳转到首页');
          }

          reject(error);
        } else {
          // 其他错误
          const errorMsg = res.data?.detail || res.data?.message || `请求失败 (${res.statusCode})`;
          console.error(`API业务错误 [${method.toUpperCase()}] ${fullUrl}:`, errorMsg);

          if (showError) {
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            });
          }

          const error = new Error(errorMsg);
          error.statusCode = res.statusCode;
          reject(error);
        }
      },
      fail: (err) => {
        // 检查是否已经超时
        if (isCompleted) {
          return;
        }
        isCompleted = true;

        // 清除超时定时器
        if (timeoutTimer) {
          clearTimeout(timeoutTimer);
        }

        console.error(`API请求失败 [${method.toUpperCase()}] ${fullUrl}:`, err);

        // 创建标准化的错误对象
        const error = new Error(err.errMsg || err.message);
        error.originalError = err;
        error.isNetworkError = true;

        reject(error);
      },
      complete: () => {
        // 确保清除超时定时器
        if (timeoutTimer) {
          clearTimeout(timeoutTimer);
        }

        if (showLoading && !isCompleted) {
          wx.hideLoading();
        }
      }
    };

    // 添加响应类型
    if (responseType) {
      requestConfig.responseType = responseType;
    }

    // 发起请求
    wx.request(requestConfig);
  });
}

/**
 * 判断是否为认证错误
 * @param {Error} error 错误对象
 * @returns {boolean} 是否为认证错误
 */
function isAuthError(error) {
  return error.statusCode === 401 || error.isAuthError === true;
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

/**
 * 获取基础URL
 */
function getBaseUrl() {
  // 根据环境返回不同的基础URL
  return apiConfig.baseUrl;
}

/**
 * 构建包含source参数的完整URL
 * @param {string} url 原始URL
 * @param {Object} options 请求选项
 * @returns {string} 完整URL
 */
function buildUrlWithSource(url, options = {}) {
  // 如果是完整URL，直接使用
  let fullUrl = url.startsWith('http') ? url : getBaseUrl() + url;

  // 检查是否需要添加source参数
  const autoAddSource = options.autoAddSource !== false; // 默认为true
  if (!autoAddSource) {
    return fullUrl;
  }

  // 获取商户代码
  const merchantCode = options.merchantCode || apiConfig.getMerchantCode();
  if (!merchantCode) {
    console.warn('未找到商户代码，跳过source参数添加');
    return fullUrl;
  }

  // 检查URL是否已经包含source参数（使用手动解析，因为小程序不支持URL构造函数）
  if (fullUrl.includes('source=')) {
    console.log('URL已包含source参数，跳过自动添加');
    return fullUrl;
  }

  // 手动添加source参数
  const separator = fullUrl.includes('?') ? '&' : '?';
  const finalUrl = `${fullUrl}${separator}source=${encodeURIComponent(merchantCode)}`;
  console.log(`添加source参数: ${merchantCode} -> ${finalUrl}`);
  return finalUrl;
}

/**
 * 获取认证请求头
 */
function getAuthHeader() {
  const authHeader = {};

  // 从tokenManager获取token信息
  const tokenInfo = tokenManager.getTokenInfo();
  if (tokenInfo && tokenInfo.access_token) {
    authHeader['Authorization'] = `Bearer ${tokenInfo.access_token}`;
  }

  // 获取用户ID
  const userId = getUserId();
  if (userId) {
    authHeader['X-User-Id'] = userId;
  }

  return authHeader;
}

/**
 * 获取用户Token
 */
function getUserToken() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.userToken) {
    return app.globalData.userToken;
  }

  // 从本地存储获取
  return wx.getStorageSync('userToken') || null;
}

/**
 * 获取用户ID
 */
function getUserId() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.userId) {
    return app.globalData.userId;
  }

  // 从本地存储获取
  return wx.getStorageSync('userId') || null;
}


/**
 * 检查登录状态
 */
function checkLoginStatus() {
  const userToken = getUserToken();
  const userId = getUserId();
  return !!(userToken && userId);
}

/**
 * 处理登录失效
 */
function handleLoginExpired() {
  console.log('登录已失效，清除本地数据');

  // 清除本地存储
  wx.removeStorageSync('userToken');
  wx.removeStorageSync('userId');
  wx.removeStorageSync('membershipInfo');

  // 清除全局数据
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userToken = null;
    app.globalData.userId = null;
    app.globalData.hasUserInfo = false;
    app.globalData.isMember = false;
    app.globalData.membershipExpiry = null;
  }

  // 重新执行自动登录
  const autoLogin = require('../user/autoLogin');
  autoLogin.silentLogin().then((success) => {
    if (!success) {
      console.log('自动登录失败');
    }
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  checkLoginStatus,
  handleLoginExpired,
  getUserToken,
  getUserId
};
