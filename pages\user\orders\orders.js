const paymentApi = require('../../../utils/api/paymentApi.js');

Page({
  data: {
    orderList: []
  },

  onLoad() {
    this.loadOrderList();
  },

  onShow() {
    this.loadOrderList();
  },

  // 加载订单列表
  async loadOrderList() {
    wx.showLoading({ title: '加载中...' });

    try {
      const result = await paymentApi.getOrderList();
      
      if (result.success && result.data) {
        // 格式化订单数据
        const formattedOrders = result.data.map(order => ({
          ...order,
          create_time: this.formatTime(order.create_time),
          status_text: this.getStatusText(order.status)
        }));

        this.setData({
          orderList: formattedOrders
        });
      }
    } catch (error) {
      console.error('加载订单列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'cancelled': '已取消',
      'refunded': '已退款'
    };
    return statusMap[status] || '未知状态';
  },

  // 重新支付
  async repay(e) {
    const order = e.currentTarget.dataset.order;
    
    wx.showLoading({ title: '处理中...' });

    try {
      // 查询订单最新状态
      const orderResult = await paymentApi.queryOrder(order.id);
      
      if (!orderResult.success) {
        throw new Error('查询订单失败');
      }

      if (orderResult.data.status !== 'pending') {
        wx.showToast({
          title: '订单状态已变更',
          icon: 'none'
        });
        this.loadOrderList();
        return;
      }

      // 发起支付
      const paymentResult = await paymentApi.requestWxPayment(orderResult.data.payment_data);

      if (paymentResult.success) {
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
        this.loadOrderList();
      } else if (!paymentResult.cancelled) {
        throw new Error(paymentResult.message || '支付失败');
      }

    } catch (error) {
      console.error('重新支付失败:', error);
      wx.showToast({
        title: error.message || '支付失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
  },

  // 取消订单
  async cancelOrder(e) {
    const order = e.currentTarget.dataset.order;
    
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: resolve
      });
    });

    if (!result.confirm) return;

    wx.showLoading({ title: '处理中...' });

    try {
      // 这里应该调用取消订单的API
      // const cancelResult = await paymentApi.cancelOrder(order.id);
      
      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });
      
      this.loadOrderList();
    } catch (error) {
      console.error('取消订单失败:', error);
      wx.showToast({
        title: '取消失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const order = e.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/user/orderDetail/orderDetail?orderId=${order.id}`
    });
  },

  // 去购买
  goToBuy() {
    wx.navigateTo({
      url: '/pages/payment/membership/membership'
    });
  },

  // 阻止事件冒泡
  stopPropagation() {}
});