# 微信小程序简历服务后端 API 接口文档

## 基础信息

**服务地址**: `http://localhost:18080`
**API版本**: v1.0.0
**认证方式**: <PERSON>er <PERSON> (JWT)

## 通用说明

### 认证方式
除了登录接口外，其他接口都需要在请求头中携带认证token：
```
Authorization: Bearer <access_token>
```

### 通用响应格式
```json
{
  "message": "操作成功",
  "data": {}
}
```

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未认证或token无效
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 参数验证失败
- `500`: 服务器内部错误

---

## 1. 用户认证模块 (/auth)

### 1.1 微信登录
**接口**: `POST /auth/login`
**描述**: 微信小程序用户登录，获取访问token

**请求参数**:
```json
{
  "code": "微信登录code",
  "user_info": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京"
  }
}
```

**参数说明**:
- `code` (必填): 微信小程序wx.login()获取的code
- `user_info` (可选): 用户基本信息
  - `gender`: 0-未知, 1-男, 2-女

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "id": 1,
    "openid": "oxxxxxxxxxxxxxx",
    "nickname": "用户昵称",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00"
  }
}
```

### 1.2 获取用户信息
**接口**: `GET /auth/user`
**描述**: 获取当前登录用户的详细信息
**认证**: 需要

**响应示例**:
```json
{
  "id": 1,
  "openid": "oxxxxxxxxxxxxxx",
  "nickname": "用户昵称",
  "avatar_url": "https://example.com/avatar.jpg",
  "gender": 1,
  "country": "中国",
  "province": "北京",
  "city": "北京",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 1.3 更新用户信息
**接口**: `PUT /auth/user`
**描述**: 更新当前用户的基本信息
**认证**: 需要

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar_url": "新头像URL",
  "gender": 2,
  "country": "中国",
  "province": "上海",
  "city": "上海"
}
```

**响应示例**:
```json
{
  "id": 1,
  "openid": "oxxxxxxxxxxxxxx",
  "nickname": "新昵称",
  "avatar_url": "新头像URL",
  "gender": 2,
  "country": "中国",
  "province": "上海",
  "city": "上海",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T12:00:00"
}
```

### 1.4 刷新访问令牌
**接口**: `POST /auth/refresh`
**描述**: 刷新当前用户的访问令牌
**认证**: 需要

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "id": 1,
    "openid": "oxxxxxxxxxxxxxx",
    "nickname": "用户昵称",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京",
    "is_member": false,
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00"
  }
}
```

### 1.5 查询会员状态
**接口**: `GET /auth/member-status`
**描述**: 查询当前用户的会员状态
**认证**: 需要

**响应示例**:
```json
{
  "is_member": false,
  "openid": "oxxxxxxxxxxxxxx",
  "message": "普通用户"
}
```

**响应字段说明**:
- `is_member`: 是否会员，true-会员，false-非会员
- `openid`: 用户的微信openid
- `message`: 状态描述，"会员用户" 或 "普通用户"

---

## 2. 简历处理模块 (/resume)

### 2.1 导出PDF
**接口**: `POST /resume/export-pdf`
**描述**: 将简历数据导出为PDF文件
**认证**: 可选（会记录用户行为）

**请求参数**:
```json
{
  "resume_data": {
    "personal_info": {
      "name": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "address": "北京市朝阳区"
    },
    "work_experience": [
      {
        "company": "ABC公司",
        "position": "软件工程师",
        "start_date": "2020-01",
        "end_date": "2023-12",
        "description": "负责前端开发工作"
      }
    ],
    "education": [
      {
        "school": "北京大学",
        "major": "计算机科学",
        "degree": "本科",
        "start_date": "2016-09",
        "end_date": "2020-06"
      }
    ]
  },
  "template_id": "templateA02.html",
  "theme_config": {
    "theme_color": "#007bff",
    "fontSize": "14px"
  }
}
```

**响应**: 直接返回PDF文件流
**Content-Type**: `application/pdf`
**Content-Disposition**: `attachment; filename=resume.pdf`

### 2.2 导出JPEG（新版本）
**接口**: `POST /resume/export-jpeg`
**描述**: 将简历数据导出为JPEG图片，返回临时文件URL
**认证**: 可选（会记录用户行为）

**请求参数**:
```json
{
  "resume_data": {
    "basicInfo": {
      "name": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "gender": "男",
      "age": "25",
      "address": "北京市朝阳区"
    },
    "jobIntention": {
      "position": "软件工程师",
      "salary": "15-20K",
      "city": "北京",
      "jobType": "全职"
    },
    "education": [...],
    "work": [...],
    "project": [...],
    "skills": [...]
  },
  "template_id": "templateA02",
  "theme_config": {
    "themeColor": "#2E75B6",
    "fontSize": 12,
    "spacing": 1.2
  },
  "quality": 75,
  "max_width": 1200,
  "max_height": 1600
}
```

**参数说明**:
- `resume_data`: 简历数据（必填）
- `template_id`: 模板ID（必填）
- `theme_config`: 主题配置（必填）
- `quality`: 图片压缩质量，1-100，默认75（可选）
- `max_width`: 最大宽度，默认1200像素（可选）
- `max_height`: 最大高度，默认1600像素（可选）

**说明**: 该接口使用全页面截图模式，会截取简历的完整内容生成长图。

**响应示例**:
```json
{
  "success": true,
  "message": "简历图片生成成功",
  "data": {
    "file_id": "550e8400-e29b-41d4-a716-446655440000",
    "image_url": "http://localhost:18080/static/temp/550e8400-e29b-41d4-a716-446655440000.jpg",
    "template_id": "templateA02",
    "compression_settings": {
      "quality": 75,
      "max_width": 1200,
      "max_height": 1600
    }
  },
  "stats": {
    "total_temp_files": 5,
    "storage_saved_mb": 2.3
  }
}
```

**响应字段说明**:
- `file_id`: 临时文件唯一标识符
- `image_url`: 图片访问URL（有效期24小时）
- `compression_settings`: 实际使用的压缩设置
- `stats`: 临时文件服务统计信息

### 2.3 预览简历
**接口**: `POST /resume/preview`
**描述**: 预览简历渲染结果，返回HTML内容用于调试
**认证**: 可选（会记录用户行为）

**请求参数**:
```json
{
  "resume_data": {
    "basicInfo": {
      "name": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "gender": "男",
      "age": "25",
      "address": "北京市朝阳区"
    },
    "jobIntention": {
      "position": "软件工程师",
      "salary": "15-20K",
      "city": "北京",
      "jobType": "全职"
    },
    "education": [
      {
        "school": "北京大学",
        "major": "计算机科学与技术",
        "degree": "本科",
        "startDate": "2018-09",
        "endDate": "2022-06",
        "description": "主修计算机科学与技术，GPA 3.8/4.0"
      }
    ],
    "work": [
      {
        "company": "腾讯科技",
        "position": "前端开发工程师",
        "startDate": "2022-07",
        "endDate": "至今",
        "description": "负责微信小程序开发，参与多个项目的前端架构设计"
      }
    ],
    "project": [
      {
        "projectName": "简历生成系统",
        "role": "技术负责人",
        "startDate": "2023-01",
        "endDate": "2023-06",
        "description": "基于FastAPI和Vue.js开发的在线简历生成系统"
      }
    ],
    "skills": [
      "Python - 熟练使用Python进行后端开发",
      "JavaScript - 熟练使用JavaScript进行前端开发"
    ],
    "moduleOrders": {
      "education": 1,
      "work": 2,
      "project": 3,
      "skills": 4
    }
  },
  "template_id": "templateA02",
  "theme_config": {
    "themeColor": "#2E75B6",
    "fontSize": 12,
    "spacing": 1.2
  }
}
```

**响应示例**:
```json
{
  "message": "简历预览生成成功",
  "html": "<html>...</html>",
  "template_id": "templateA02",
  "theme_config": {
    "themeColor": "#2E75B6",
    "fontSize": 12,
    "spacing": 1.2
  },
  "resume_data": {
    "basicInfo": {...},
    "jobIntention": {...}
  }
}
```

### 2.4 获取临时图片
**接口**: `GET /resume/temp-image/{file_id}`
**描述**: 获取临时图片文件
**认证**: 不需要

**路径参数**:
- `file_id`: 文件唯一标识符

**响应**: 直接返回JPEG图片文件流
**Content-Type**: `image/jpeg`
**Cache-Control**: `public, max-age=3600`

**错误响应**:
- `404`: 文件不存在或已过期

### 2.5 清理临时文件
**接口**: `POST /resume/cleanup-temp-files`
**描述**: 手动清理过期的临时文件
**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "message": "临时文件清理完成",
  "stats": {
    "total_files": 3,
    "total_original_size": 5242880,
    "total_compressed_size": 2621440,
    "total_access_count": 15,
    "average_compression_ratio": 50.0,
    "storage_saved_bytes": 2621440
  }
}
```

### 2.6 获取临时文件统计
**接口**: `GET /resume/temp-files/stats`
**描述**: 获取临时文件服务的统计信息
**认证**: 不需要

**响应示例**:
```json
{
  "success": true,
  "stats": {
    "total_files": 5,
    "total_original_size": 10485760,
    "total_compressed_size": 5242880,
    "total_access_count": 25,
    "average_compression_ratio": 50.0,
    "storage_saved_bytes": 5242880
  }
}
```

**统计字段说明**:
- `total_files`: 当前临时文件总数
- `total_original_size`: 原始文件总大小（字节）
- `total_compressed_size`: 压缩后文件总大小（字节）
- `total_access_count`: 总访问次数
- `average_compression_ratio`: 平均压缩率（百分比）
- `storage_saved_bytes`: 节省的存储空间（字节）

### 2.7 获取模板列表
**接口**: `GET /resume/templates`
**描述**: 获取所有可用的简历模板列表

**响应示例**:
```json
{
  "message": "获取模板列表成功",
  "templates": [
    "templateA01.html",
    "templateA02.html",
    "templateA03.html",
    "templateA04.html"
  ]
}
```

---

## 3. 用户反馈模块 (/feedback)

### 3.1 提交反馈
**接口**: `POST /feedback`
**描述**: 用户提交反馈信息
**认证**: 需要

**请求参数**:
```json
{
  "content": "反馈内容，最多2000字符",
  "contact_info": "联系方式（可选）"
}
```

**响应示例**:
```json
{
  "message": "反馈提交成功，我们会尽快处理"
}
```

### 3.2 获取反馈列表
**接口**: `GET /feedback`
**描述**: 获取当前用户的反馈列表
**认证**: 需要

**查询参数**:
- `status_filter` (可选): 状态筛选，可选值: `pending`, `replied`, `closed`
- `limit` (可选): 返回数量限制，默认20，最大100
- `offset` (可选): 偏移量，默认0

**请求示例**:
```
GET /feedback?status_filter=pending&limit=10&offset=0
```

**响应示例**:
```json
{
  "total": 5,
  "items": [
    {
      "id": 1,
      "user_id": 1,
      "content": "反馈内容",
      "contact_info": "联系方式",
      "status": "replied",
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T12:00:00",
      "replies": [
        {
          "id": 1,
          "admin_name": "客服小王",
          "reply_content": "感谢您的反馈，我们会及时处理",
          "created_at": "2024-01-01T12:00:00"
        }
      ]
    }
  ]
}
```

### 3.3 获取反馈详情
**接口**: `GET /feedback/{feedback_id}`
**描述**: 获取指定反馈的详细信息
**认证**: 需要

**路径参数**:
- `feedback_id`: 反馈ID

**响应示例**:
```json
{
  "id": 1,
  "user_id": 1,
  "content": "反馈内容",
  "contact_info": "联系方式",
  "status": "replied",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T12:00:00",
  "replies": [
    {
      "id": 1,
      "admin_name": "客服小王",
      "reply_content": "感谢您的反馈，我们会及时处理",
      "created_at": "2024-01-01T12:00:00"
    }
  ]
}
```

### 3.4 管理员回复反馈
**接口**: `POST /feedback/{feedback_id}/reply`
**描述**: 管理员回复用户反馈（需要管理员权限）

**路径参数**:
- `feedback_id`: 反馈ID

**请求参数**:
```json
{
  "reply_content": "回复内容，最多2000字符",
  "admin_name": "管理员名称"
}
```

**响应示例**:
```json
{
  "message": "回复成功"
}
```

### 3.5 更新反馈状态
**接口**: `PUT /feedback/{feedback_id}/status`
**描述**: 更新反馈状态（需要管理员权限）

**路径参数**:
- `feedback_id`: 反馈ID

**请求参数**:
```json
{
  "status": "closed"
}
```

**状态值说明**:
- `pending`: 待处理
- `replied`: 已回复
- `closed`: 已关闭

**响应示例**:
```json
{
  "message": "状态更新成功"
}
```

---

## 4. 数据模型说明

### 4.1 用户信息模型
```json
{
  "id": 1,
  "openid": "微信openid",
  "nickname": "用户昵称",
  "avatar_url": "头像URL",
  "gender": 1,
  "country": "国家",
  "province": "省份",
  "city": "城市",
  "is_member": false,
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

**字段说明**:
- `is_member`: 是否会员，true-会员，false-非会员，默认为false

### 4.2 反馈信息模型
```json
{
  "id": 1,
  "user_id": 1,
  "content": "反馈内容",
  "contact_info": "联系方式",
  "status": "pending|replied|closed",
  "created_at": "创建时间",
  "updated_at": "更新时间",
  "replies": [
    {
      "id": 1,
      "admin_name": "管理员名称",
      "reply_content": "回复内容",
      "created_at": "回复时间"
    }
  ]
}
```

### 4.3 简历数据模型
简历数据结构请参考现有的ResumeData模型，包含个人信息、工作经历、教育背景等字段。

---

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用（如PDF服务异常） |

---

## 6. 开发注意事项

1. **认证token**: 登录成功后需要保存access_token，后续请求都需要携带
2. **token过期**: token默认30分钟过期，可通过refresh接口刷新
3. **文件下载**: PDF和JPEG导出接口返回文件流，需要特殊处理
4. **用户行为**: 简历相关操作会自动记录用户行为，无需额外调用
5. **错误处理**: 请根据HTTP状态码和返回的detail信息进行错误处理

## 7. 微信小程序集成示例

```javascript
// 登录示例
wx.login({
  success: (res) => {
    if (res.code) {
      wx.request({
        url: 'https://your-domain.com/auth/login',
        method: 'POST',
        data: {
          code: res.code,
          user_info: {
            nickName: '用户昵称',
            avatarUrl: '头像URL'
          }
        },
        success: (loginRes) => {
          wx.setStorageSync('access_token', loginRes.data.access_token);
        }
      });
    }
  }
});

// 带认证的请求示例
wx.request({
  url: 'https://your-domain.com/feedback',
  method: 'POST',
  header: {
    'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
  },
  data: {
    content: '反馈内容'
  },
  success: (res) => {
    console.log('反馈提交成功');
  }
});

// 查询会员状态示例
wx.request({
  url: 'https://your-domain.com/auth/member-status',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
  },
  success: (res) => {
    if (res.statusCode === 200) {
      const isMember = res.data.is_member;
      console.log('用户会员状态:', isMember ? '会员' : '非会员');

      // 根据会员状态显示不同的UI或功能
      if (isMember) {
        // 显示会员专属功能
        console.log('显示会员专属功能');
      } else {
        // 显示升级会员提示
        console.log('显示升级会员提示');
      }
    }
  },
  fail: (err) => {
    console.error('查询会员状态失败:', err);
  }
});
```

---

## 6. 证件照生成模块 (/idphoto)

### 6.1 健康检查
**接口**: `GET /idphoto/health`
**描述**: 检查证件照服务的健康状态
**认证**: 不需要

**响应示例**:
```json
{
  "status": "ok",
  "message": "证件照服务运行正常"
}
```

### 6.2 生成证件照
**接口**: `POST /idphoto/generate`
**描述**: 上传照片并生成指定尺寸和背景色的证件照
**认证**: 需要

**请求参数**:
- `image` (file): 上传的照片文件 (必填)
- `size` (string): 证件照尺寸，可选值：one_inch、two_inch、big_one_inch、small_one_inch、big_two_inch、small_two_inch (默认：one_inch)
- `color` (string): 背景颜色，可选值：transparent、white、blue、red、blue_gradient、red_gradient (默认：white)

**响应示例**:
```json
{
  "success": true,
  "message": "证件照生成成功",
  "data": {
    "image_base64": "base64编码的证件照图片",
    "size": "one_inch",
    "size_name": "一寸",
    "color": "white",
    "color_name": "白色",
    "dimensions": {
      "width": 295,
      "height": 413
    },
    "hd_image_base64": "base64编码的高清证件照图片",
    "transparent_base64": "base64编码的透明背景证件照图片"
  }
}
```

### 6.3 获取支持的尺寸列表
**接口**: `GET /idphoto/sizes`
**描述**: 获取所有支持的证件照尺寸规格
**认证**: 不需要

**响应示例**:
```json
{
  "success": true,
  "message": "获取尺寸列表成功",
  "data": {
    "sizes": [
      {
        "name": "一寸",
        "value": "one_inch",
        "width": 295,
        "height": 413,
        "print_size": "2.5cm*3.5cm",
        "description": "标准一寸证件照"
      },
      {
        "name": "二寸",
        "value": "two_inch",
        "width": 413,
        "height": 579,
        "print_size": "3.5cm*4.9cm",
        "description": "标准二寸证件照"
      }
    ]
  }
}
```

### 6.4 获取支持的颜色列表
**接口**: `GET /idphoto/colors`
**描述**: 获取所有支持的背景颜色选项
**认证**: 不需要

**响应示例**:
```json
{
  "success": true,
  "message": "获取颜色列表成功",
  "data": {
    "colors": [
      {
        "name": "白色",
        "value": "white",
        "hex": "FFFFFF",
        "render": 0,
        "description": "白色背景"
      },
      {
        "name": "蓝色渐变",
        "value": "blue_gradient",
        "hex": "438EDB",
        "render": 1,
        "description": "蓝色渐变背景"
      }
    ]
  }
}
```

### 6.5 生成透明背景证件照（分步处理第一步）
**接口**: `POST /idphoto/generate_transparent`
**描述**: 将用户上传的照片转换为指定尺寸的透明背景证件照，这是分步生成证件照的第一步
**认证**: 需要

**请求参数**:
- `image` (file): 上传的照片文件 (必填)
- `size` (string): 证件照尺寸，可选值：one_inch、two_inch、big_one_inch、small_one_inch、big_two_inch、small_two_inch (默认：one_inch)

**响应示例**:
```json
{
  "success": true,
  "message": "透明背景证件照生成成功",
  "data": {
    "image_base64": "base64编码的透明背景证件照图片",
    "size": "one_inch",
    "size_name": "一寸",
    "dimensions": {
      "width": 295,
      "height": 413
    },
    "hd_image_base64": "base64编码的高清透明背景证件照图片"
  }
}
```

**使用场景**:
- 微信小程序端可以先调用此接口生成透明背景证件照
- 然后在前端预览不同背景色效果
- 最后调用添加背景色接口生成最终证件照

### 6.6 为透明证件照添加背景色（分步处理第二步）
**接口**: `POST /idphoto/generate_add_color`
**描述**: 为透明背景的证件照添加指定的背景颜色，这是分步生成证件照的第二步
**认证**: 需要

**请求参数**:
- `image_base64` (string): 透明背景证件照的base64编码 (必填)
- `color` (string): 背景颜色，可选值：white、blue、red、blue_gradient、red_gradient (必填，不支持transparent)

**响应示例**:
```json
{
  "success": true,
  "message": "背景色添加成功",
  "data": {
    "image_base64": "base64编码的带背景色证件照图片",
    "color": "white",
    "color_name": "白色"
  }
}
```

**注意事项**:
- 输入的base64编码必须是透明背景的证件照（通常来自generate_transparent接口）
- 不支持transparent颜色选项，因为输入已经是透明背景
- 该接口接受base64编码，无需文件上传，简化了前端调用

**分步处理的优势**:
1. **更好的用户体验**: 用户可以先看到透明背景的证件照效果，确认人像处理是否满意
2. **灵活的颜色选择**: 前端可以实时预览不同背景色效果，无需重复处理人像
3. **减少服务器负载**: 避免因更换背景色而重复进行人像分割处理
4. **更快的响应速度**: 第二步只需要添加背景色，处理速度更快

---

## 证件照API使用示例

### 微信小程序端调用示例

#### 方式一：一步生成证件照（原有方式）
```javascript
// 选择图片并生成证件照
wx.chooseImage({
  count: 1,
  sizeType: ['original'],
  sourceType: ['album', 'camera'],
  success: (res) => {
    const tempFilePath = res.tempFilePaths[0];

    // 上传并生成证件照
    wx.uploadFile({
      url: 'https://your-domain.com/idphoto/generate',
      filePath: tempFilePath,
      name: 'image',
      formData: {
        'size': 'one_inch',
        'color': 'white'
      },
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
      },
      success: (uploadRes) => {
        const data = JSON.parse(uploadRes.data);
        if (data.success) {
          console.log('证件照生成成功');

          // 显示生成的证件照
          const imageBase64 = data.data.image_base64;
          const imageSrc = 'data:image/jpeg;base64,' + imageBase64;

          // 更新页面显示
          this.setData({
            idPhotoSrc: imageSrc
          });
        } else {
          wx.showToast({
            title: '生成失败',
            icon: 'error'
          });
        }
      },
      fail: (err) => {
        console.error('上传失败:', err);
        wx.showToast({
          title: '上传失败',
          icon: 'error'
        });
      }
    });
  }
});
```

#### 方式二：分步生成证件照（推荐方式）
```javascript
// 第一步：生成透明背景证件照
function generateTransparentPhoto(tempFilePath) {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'https://your-domain.com/idphoto/generate_transparent',
      filePath: tempFilePath,
      name: 'image',
      formData: {
        'size': 'one_inch'
      },
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
      },
      success: (uploadRes) => {
        const data = JSON.parse(uploadRes.data);
        if (data.success) {
          resolve(data.data.image_base64);
        } else {
          reject(new Error(data.message || '透明背景生成失败'));
        }
      },
      fail: reject
    });
  });
}

// 第二步：添加背景色（使用base64，无需文件上传）
function addBackgroundColor(transparentImageBase64, color) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: 'https://your-domain.com/idphoto/generate_add_color',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('access_token'),
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: {
        'image_base64': transparentImageBase64,
        'color': color
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          resolve(res.data.data.image_base64);
        } else {
          reject(new Error(res.data.message || '背景色添加失败'));
        }
      },
      fail: reject
    });
  });
}

// 完整的分步处理流程
wx.chooseImage({
  count: 1,
  sizeType: ['original'],
  sourceType: ['album', 'camera'],
  success: async (res) => {
    const tempFilePath = res.tempFilePaths[0];

    try {
      // 显示加载提示
      wx.showLoading({
        title: '正在处理照片...'
      });

      // 第一步：生成透明背景证件照
      const transparentBase64 = await generateTransparentPhoto(tempFilePath);

      // 显示透明背景预览（可选）
      this.setData({
        transparentPhotoSrc: 'data:image/png;base64,' + transparentBase64
      });

      // 第二步：添加背景色
      const finalBase64 = await addBackgroundColor(transparentBase64, 'white');

      // 显示最终证件照
      this.setData({
        idPhotoSrc: 'data:image/jpeg;base64,' + finalBase64
      });

      wx.hideLoading();
      wx.showToast({
        title: '证件照生成成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('证件照生成失败:', error);
      wx.showToast({
        title: error.message || '生成失败',
        icon: 'error'
      });
    }
  }
});

// 实时切换背景色（基于已有的透明背景）
function switchBackgroundColor(transparentBase64, newColor) {
  addBackgroundColor(transparentBase64, newColor)
    .then(finalBase64 => {
      this.setData({
        idPhotoSrc: 'data:image/jpeg;base64,' + finalBase64
      });
    })
    .catch(error => {
      console.error('切换背景色失败:', error);
      wx.showToast({
        title: '切换失败',
        icon: 'error'
      });
    });
}
```

### cURL调用示例

```bash
# 一步生成证件照（原有方式）
curl -X POST "http://localhost:18080/idphoto/generate" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "image=@/path/to/your/photo.jpg" \
  -F "size=one_inch" \
  -F "color=white"

# 分步生成证件照 - 第一步：生成透明背景
curl -X POST "http://localhost:18080/idphoto/generate_transparent" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "image=@/path/to/your/photo.jpg" \
  -F "size=one_inch"

# 分步生成证件照 - 第二步：添加背景色（使用base64）
curl -X POST "http://localhost:18080/idphoto/generate_add_color" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "image_base64=iVBORw0KGgoAAAANSUhEUgAA..." \
  -d "color=white"

# 获取支持的尺寸列表
curl -X GET "http://localhost:18080/idphoto/sizes"

# 获取支持的颜色列表
curl -X GET "http://localhost:18080/idphoto/colors"
```

---

## 7. 支付模块 (/payment)

### 7.1 获取会员套餐列表
**接口**: `GET /payment/plans`
**描述**: 获取可用的会员套餐列表
**认证**: 不需要

**请求参数**:
- `active_only` (查询参数, 可选): 是否只获取启用的套餐，默认为true

**响应示例**:
```json
{
  "plans": [
    {
      "id": 1,
      "name": "月度会员",
      "description": "享受一个月的会员特权",
      "price": 1980,
      "original_price": 2980,
      "duration_days": 30,
      "features": ["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持"],
      "is_active": true,
      "is_recommended": false,
      "sort_order": 1,
      "discount_rate": 33.6,
      "price_yuan": 19.8,
      "original_price_yuan": 29.8,
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    }
  ],
  "total": 3
}
```

### 7.2 创建订单
**接口**: `POST /payment/create-order`
**描述**: 创建会员购买订单
**认证**: 需要

**请求参数**:
```json
{
  "plan_id": 1,
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0..."
}
```

**参数说明**:
- `plan_id` (必填): 套餐ID
- `client_ip` (可选): 客户端IP
- `user_agent` (可选): 用户代理

**响应示例**:
```json
{
  "id": "RS20241201123456ABCD1234",
  "user_id": 1,
  "plan_id": 1,
  "amount": 1980,
  "original_amount": 2980,
  "discount_amount": 1000,
  "status": "pending",
  "expired_at": "2024-12-01T13:04:56",
  "plan_name": "月度会员",
  "amount_yuan": 19.8,
  "created_at": "2024-12-01T12:34:56",
  "updated_at": "2024-12-01T12:34:56"
}
```

### 7.3 获取用户订单列表
**接口**: `GET /payment/orders`
**描述**: 获取当前用户的订单列表
**认证**: 需要

**请求参数**:
- `status_filter` (查询参数, 可选): 订单状态过滤
- `limit` (查询参数, 可选): 每页数量，默认20，最大100
- `offset` (查询参数, 可选): 偏移量，默认0

**响应示例**:
```json
{
  "orders": [
    {
      "id": "RS20241201123456ABCD1234",
      "user_id": 1,
      "plan_id": 1,
      "amount": 1980,
      "status": "pending",
      "plan_name": "月度会员",
      "amount_yuan": 19.8,
      "created_at": "2024-12-01T12:34:56"
    }
  ],
  "total": 1,
  "has_more": false
}
```

### 7.4 获取订单详情
**接口**: `GET /payment/order/{order_id}`
**描述**: 获取指定订单的详细信息
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "id": "RS20241201123456ABCD1234",
  "user_id": 1,
  "plan_id": 1,
  "amount": 1980,
  "original_amount": 2980,
  "discount_amount": 1000,
  "status": "pending",
  "wx_prepay_id": "wx123456789",
  "expired_at": "2024-12-01T13:04:56",
  "plan_name": "月度会员",
  "amount_yuan": 19.8,
  "created_at": "2024-12-01T12:34:56",
  "updated_at": "2024-12-01T12:34:56"
}
```

### 7.5 创建微信支付
**接口**: `POST /payment/wechat-pay`
**描述**: 为指定订单创建微信支付参数
**认证**: 需要

**请求参数**:
```json
{
  "order_id": "RS20241201123456ABCD1234"
}
```

**参数说明**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "appId": "wx1234567890abcdef",
  "timeStamp": "1701234567",
  "nonceStr": "abcd1234efgh5678",
  "package": "prepay_id=wx123456789",
  "signType": "RSA",
  "paySign": "signature_string",
  "order_id": "RS20241201123456ABCD1234"
}
```

### 7.6 取消订单
**接口**: `POST /payment/cancel-order/{order_id}`
**描述**: 取消指定的未支付订单
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**请求参数**:
```json
{
  "cancel_reason": "用户主动取消"
}
```

**参数说明**:
- `cancel_reason` (可选): 取消原因

**响应示例**:
```json
{
  "success": true,
  "message": "订单取消成功"
}
```

### 7.7 查询微信订单状态
**接口**: `GET /payment/query-order/{order_id}`
**描述**: 查询微信支付平台上的订单状态
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "out_trade_no": "RS20241201123456ABCD1234",
    "transaction_id": "4200001234567890",
    "trade_state": "SUCCESS",
    "trade_state_desc": "支付成功"
  }
}
```

### 7.8 微信支付回调
**接口**: `POST /payment/callback`
**描述**: 微信支付平台回调接口，用于接收支付结果通知
**认证**: 不需要（由微信支付平台调用）

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "成功"
}
```

## 8. 会员管理模块 (/membership)

### 8.1 获取会员状态
**接口**: `GET /membership/status`
**描述**: 获取当前用户的会员状态和信息
**认证**: 需要

**响应示例**:
```json
{
  "is_member": true,
  "current_membership": {
    "id": 1,
    "user_id": 1,
    "plan_id": 1,
    "plan_name": "月度会员",
    "start_date": "2024-12-01T12:34:56",
    "end_date": "2024-12-31T12:34:56",
    "is_active": true,
    "remaining_days": 30
  },
  "membership_history": []
}
```

### 8.2 获取会员权益
**接口**: `GET /membership/benefits`
**描述**: 获取当前用户的会员权益信息
**认证**: 需要

**响应示例**:
```json
{
  "is_member": true,
  "plan_name": "月度会员",
  "end_date": "2024-12-31T12:34:56",
  "remaining_days": 30,
  "benefits": ["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持"],
  "message": "您是月度会员用户，还有30天到期"
}
```

### 8.3 检查功能权限
**接口**: `GET /membership/check-permission/{feature}`
**描述**: 检查当前用户是否有权限使用指定功能
**认证**: 需要

**路径参数**:
- `feature` (必填): 功能名称，如resume_export, idphoto_generate, premium_templates

**响应示例**:
```json
{
  "has_permission": true,
  "is_member": true,
  "plan_name": "月度会员",
  "message": "权限验证成功",
  "upgrade_required": false
}
```

### 8.4 同步会员状态
**接口**: `POST /membership/sync-status`
**描述**: 同步用户会员状态（检查过期等）
**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "is_member": true,
  "message": "会员状态同步成功",
  "expired_count": 0
}
```

### 8.5 获取使用统计
**接口**: `GET /membership/usage-stats`
**描述**: 获取当前用户的功能使用统计
**认证**: 需要

**响应示例**:
```json
{
  "is_member": true,
  "stats_period": "最近30天",
  "usage_stats": {
    "resume_exports": 15,
    "idphoto_generates": 8,
    "template_uses": 5,
    "total_actions": 28
  },
  "membership_info": {
    "plan_name": "月度会员",
    "end_date": "2024-12-31T12:34:56",
    "remaining_days": 30
  }
}
```

## 9. 支付安全模块 (/payment/security)

### 9.1 检查订单创建权限
**接口**: `GET /payment/security/check-order-permission`
**描述**: 检查当前用户是否有权限创建新订单
**认证**: 需要

**响应示例**:
```json
{
  "can_create_order": true,
  "message": "验证通过",
  "user_id": 1,
  "client_ip": "127.0.0.1"
}
```

### 9.2 检查支付权限
**接口**: `GET /payment/security/check-payment-permission/{order_id}`
**描述**: 检查当前用户是否有权限支付指定订单
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "can_pay": true,
  "message": "验证通过",
  "order_id": "RS20241201123456ABCD1234",
  "user_id": 1
}
```

### 9.3 获取安全提示
**接口**: `GET /payment/security/security-tips`
**描述**: 获取支付安全提示信息
**认证**: 不需要

**响应示例**:
```json
{
  "security_tips": [
    {
      "title": "订单安全",
      "tips": [
        "不要频繁创建订单",
        "及时完成或取消未支付订单",
        "注意订单过期时间"
      ]
    },
    {
      "title": "支付安全",
      "tips": [
        "确认订单信息后再支付",
        "不要重复点击支付按钮",
        "支付完成后及时查看结果"
      ]
    }
  ],
  "emergency_contact": {
    "customer_service": "在线客服",
    "report_issue": "问题反馈功能"
  }
}
```
