/**
 * 证件照生成相关API接口（新版本）
 * 对应服务端API文档中的证件照生成模块 (/idphoto)
 */
const request = require('./request.js');

/**
 * 证件照API类
 */
class IdPhotoApi {

  /**
   * 证件照服务健康检查
   * 对应接口: GET /idphoto/health
   * @returns {Promise<Object>} 健康检查响应
   */
  async healthCheck() {
    try {
      console.log('=== 证件照服务健康检查API请求 ===');
      
      const response = await request.get('/idphoto/health', {}, {
        showLoading: false,
        showError: false,
        needAuth: false, // 根据文档不需要认证
        timeout: 5000
      });
      return response;
    } catch (error) {
      console.error('证件照服务健康检查失败:', error);
      return {
        success: false,
        message: error.message || '证件照服务健康检查失败'
      };
    }
  }

  /**
   * 获取所有支持的证件照尺寸规格
   * 对应接口: GET /idphoto/sizes
   * @returns {Promise<Object>} 尺寸列表响应
   */
  async getSizes() {
    try {
      console.log('=== 获取证件照尺寸列表API请求 ===');
      
      const response = await request.get('/idphoto/sizes', {}, {
        showLoading: false,
        showError: false,
        needAuth: false, // 根据文档不需要认证
        timeout: 10000
      });
      return response;
    } catch (error) {
      console.error('获取证件照尺寸列表失败:', error);
      return {
        success: false,
        message: error.message || '获取证件照尺寸列表失败'
      };
    }
  }

  /**
   * 获取所有支持的背景颜色选项
   * 对应接口: GET /idphoto/colors
   * @returns {Promise<Object>} 颜色列表响应
   */
  async getColors() {
    try {
      console.log('=== 获取证件照颜色列表API请求 ===');
      
      const response = await request.get('/idphoto/colors', {}, {
        showLoading: false,
        showError: false,
        needAuth: false, // 根据文档不需要认证
        timeout: 10000
      });
      return response;
    } catch (error) {
      console.error('获取证件照颜色列表失败:', error);
      return {
        success: false,
        message: error.message || '获取证件照颜色列表失败'
      };
    }
  }

  /**
   * 上传照片并生成指定尺寸和背景色的证件照（一步生成）
   * 对应接口: POST /idphoto/generate
   * @param {string} imagePath 图片文件路径
   * @param {string} size 证件照尺寸，默认one_inch
   * @param {string} color 背景颜色，默认white
   * @returns {Promise<Object>} 证件照生成响应
   */
  async generateIdPhoto(imagePath, size = 'one_inch', color = 'white') {
    try {
      console.log('=== 生成证件照API请求（一步生成）===');
      console.log('图片路径:', imagePath);
      console.log('尺寸:', size);
      console.log('颜色:', color);

      // 使用request模块的文件上传功能
      const response = await request.uploadFile('/idphoto/generate', imagePath, 'image', {
        size: size,
        color: color
      }, {
        showLoading: false,
        showError: false,
        needAuth: true,
        timeout: 30000
      });

      return response;
    } catch (error) {
      console.error('生成证件照失败:', error);
      return {
        success: false,
        message: error.message || '生成证件照失败'
      };
    }
  }

  /**
   * 将用户上传的照片转换为指定尺寸的透明背景证件照（分步处理第一步）
   * 对应接口: POST /idphoto/generate_transparent
   * @param {string} imagePath 图片文件路径
   * @param {string} size 证件照尺寸，默认one_inch
   * @returns {Promise<Object>} 透明背景证件照响应
   */
  async generateTransparentPhoto(imagePath, size = 'one_inch') {
    try {
      console.log('=== 生成透明背景证件照API请求 ===');
      console.log('图片路径:', imagePath);
      console.log('尺寸:', size);

      const response = await request.uploadFile('/idphoto/generate_transparent', imagePath, 'image', {
        size: size
      }, {
        showLoading: false,
        showError: false,
        needAuth: true,
        timeout: 30000
      });

      return response;
    } catch (error) {
      console.error('生成透明背景证件照失败:', error);
      return {
        success: false,
        message: error.message || '生成透明背景证件照失败'
      };
    }
  }

  /**
   * 为透明背景的证件照添加指定的背景颜色（分步处理第二步）
   * 对应接口: POST /idphoto/generate_add_color
   * @param {string} imageBase64 透明背景证件照的base64编码
   * @param {string} color 背景颜色，不支持transparent
   * @returns {Promise<Object>} 添加背景色响应
   */
  async addBackgroundColor(imageBase64, color) {
    try {
      console.log('=== 添加背景色API请求 ===');
      console.log('颜色:', color);
      console.log('有图片数据:', !!imageBase64);

      // 处理base64数据，移除data:image前缀（如果有的话）
      let base64Data = imageBase64;
      if (base64Data.startsWith('data:image/')) {
        base64Data = base64Data.split(',')[1];
      }

      const response = await request.post('/idphoto/generate_add_color', {
        image_base64: base64Data,
        color: color
      }, {
        showLoading: false,
        showError: false,
        needAuth: true,
        timeout: 30000,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      return response;
    } catch (error) {
      console.error('添加背景色失败:', error);
      return {
        success: false,
        message: error.message || '添加背景色失败'
      };
    }
  }

  /**
   * 批量获取尺寸和颜色信息
   * @returns {Promise<Object>} 包含尺寸和颜色信息的响应
   */
  async getConfigInfo() {
    try {
      console.log('=== 批量获取证件照配置信息 ===');

      const [sizesResult, colorsResult] = await Promise.all([
        this.getSizes(),
        this.getColors()
      ]);

      return {
        success: true,
        data: {
          sizes: sizesResult.success ? sizesResult.data : null,
          colors: colorsResult.success ? colorsResult.data : null
        },
        message: '获取配置信息成功'
      };
    } catch (error) {
      console.error('批量获取配置信息失败:', error);
      return {
        success: false,
        message: error.message || '批量获取配置信息失败'
      };
    }
  }
}

module.exports = new IdPhotoApi();
